/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

@font-face {
	font-family: "codicon";
	font-display: block;
	src: url("./codicon.ttf?38dcd33a732ebca5a557e04831e9e235") format("truetype");
}

.codicon[class*="codicon-"] {
	font: normal normal normal 16px/1 codicon;
	display: inline-block;
	text-decoration: none;
	text-rendering: auto;
	text-align: center;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
}

/* Smaller loading icons */
.codicon-loading,
.codicon-sync.codicon-modifier-spin {
	font-size: 12px !important;
}

/*---------------------
 *  Modifiers
 *-------------------*/

@keyframes codicon-spin {
	100% {
		transform: rotate(360deg);
	}
}

.codicon-sync.codicon-modifier-spin,
.codicon-loading.codicon-modifier-spin,
.codicon-gear.codicon-modifier-spin {
	/* Use steps to throttle FPS to reduce CPU usage */
	animation: codicon-spin 1.5s steps(30) infinite;
}

.codicon-modifier-disabled {
	opacity: 0.5;
}

.codicon-modifier-hidden {
	opacity: 0;
}

/* custom speed & easing for loading icon */
.codicon-loading {
	animation-duration: 1s !important;
	animation-timing-function: cubic-bezier(0.53, 0.21, 0.29, 0.67) !important;
}

/*---------------------
 *  Icons
 *-------------------*/

.codicon-add:before {
	content: "\ea60";
}
.codicon-plus:before {
	content: "\ea60";
}
.codicon-gist-new:before {
	content: "\ea60";
}
.codicon-repo-create:before {
	content: "\ea60";
}
.codicon-lightbulb:before {
	content: "\ea61";
}
.codicon-light-bulb:before {
	content: "\ea61";
}
.codicon-repo:before {
	content: "\ea62";
}
.codicon-repo-delete:before {
	content: "\ea62";
}
.codicon-gist-fork:before {
	content: "\ea63";
}
.codicon-repo-forked:before {
	content: "\ea63";
}
.codicon-git-pull-request:before {
	content: "\ea64";
}
.codicon-git-pull-request-abandoned:before {
	content: "\ea64";
}
.codicon-record-keys:before {
	content: "\ea65";
}
.codicon-keyboard:before {
	content: "\ea65";
}
.codicon-tag:before {
	content: "\ea66";
}
.codicon-git-pull-request-label:before {
	content: "\ea66";
}
.codicon-tag-add:before {
	content: "\ea66";
}
.codicon-tag-remove:before {
	content: "\ea66";
}
.codicon-person:before {
	content: "\ea67";
}
.codicon-person-follow:before {
	content: "\ea67";
}
.codicon-person-outline:before {
	content: "\ea67";
}
.codicon-person-filled:before {
	content: "\ea67";
}
.codicon-git-branch:before {
	content: "\ea68";
}
.codicon-git-branch-create:before {
	content: "\ea68";
}
.codicon-git-branch-delete:before {
	content: "\ea68";
}
.codicon-source-control:before {
	content: "\ea68";
}
.codicon-mirror:before {
	content: "\ea69";
}
.codicon-mirror-public:before {
	content: "\ea69";
}
.codicon-star:before {
	content: "\ea6a";
}
.codicon-star-add:before {
	content: "\ea6a";
}
.codicon-star-delete:before {
	content: "\ea6a";
}
.codicon-star-empty:before {
	content: "\ea6a";
}
.codicon-comment:before {
	content: "\ea6b";
}
.codicon-comment-add:before {
	content: "\ea6b";
}
.codicon-alert:before {
	content: "\ea6c";
}
.codicon-warning:before {
	content: "\ea6c";
}
.codicon-search:before {
	content: "\ea6d";
}
.codicon-search-save:before {
	content: "\ea6d";
}
.codicon-log-out:before {
	content: "\ea6e";
}
.codicon-sign-out:before {
	content: "\ea6e";
}
.codicon-log-in:before {
	content: "\ea6f";
}
.codicon-sign-in:before {
	content: "\ea6f";
}
.codicon-eye:before {
	content: "\ea70";
}
.codicon-eye-unwatch:before {
	content: "\ea70";
}
.codicon-eye-watch:before {
	content: "\ea70";
}
.codicon-circle-filled:before {
	content: "\ea71";
}
.codicon-primitive-dot:before {
	content: "\ea71";
}
.codicon-close-dirty:before {
	content: "\ea71";
}
.codicon-debug-breakpoint:before {
	content: "\ea71";
}
.codicon-debug-breakpoint-disabled:before {
	content: "\ea71";
}
.codicon-debug-hint:before {
	content: "\ea71";
}
.codicon-terminal-decoration-success:before {
	content: "\ea71";
}
.codicon-primitive-square:before {
	content: "\ea72";
}
.codicon-edit:before {
	content: "\ea73";
}
.codicon-pencil:before {
	content: "\ea73";
}
.codicon-info:before {
	content: "\ea74";
}
.codicon-issue-opened:before {
	content: "\ea74";
}
.codicon-gist-private:before {
	content: "\ea75";
}
.codicon-git-fork-private:before {
	content: "\ea75";
}
.codicon-lock:before {
	content: "\ea75";
}
.codicon-mirror-private:before {
	content: "\ea75";
}
.codicon-close:before {
	content: "\ea76";
}
.codicon-remove-close:before {
	content: "\ea76";
}
.codicon-x:before {
	content: "\ea76";
}
.codicon-repo-sync:before {
	content: "\ea77";
}
.codicon-sync:before {
	content: "\ea77";
}
.codicon-clone:before {
	content: "\ea78";
}
.codicon-desktop-download:before {
	content: "\ea78";
}
.codicon-beaker:before {
	content: "\ea79";
}
.codicon-microscope:before {
	content: "\ea79";
}
.codicon-vm:before {
	content: "\ea7a";
}
.codicon-device-desktop:before {
	content: "\ea7a";
}
.codicon-file:before {
	content: "\ea7b";
}
.codicon-file-text:before {
	content: "\ea7b";
}
.codicon-more:before {
	content: "\ea7c";
}
.codicon-ellipsis:before {
	content: "\ea7c";
}
.codicon-kebab-horizontal:before {
	content: "\ea7c";
}
.codicon-mail-reply:before {
	content: "\ea7d";
}
.codicon-reply:before {
	content: "\ea7d";
}
.codicon-organization:before {
	content: "\ea7e";
}
.codicon-organization-filled:before {
	content: "\ea7e";
}
.codicon-organization-outline:before {
	content: "\ea7e";
}
.codicon-new-file:before {
	content: "\ea7f";
}
.codicon-file-add:before {
	content: "\ea7f";
}
.codicon-new-folder:before {
	content: "\ea80";
}
.codicon-file-directory-create:before {
	content: "\ea80";
}
.codicon-trash:before {
	content: "\ea81";
}
.codicon-trashcan:before {
	content: "\ea81";
}
.codicon-history:before {
	content: "\ea82";
}
.codicon-clock:before {
	content: "\ea82";
}
.codicon-folder:before {
	content: "\ea83";
}
.codicon-file-directory:before {
	content: "\ea83";
}
.codicon-symbol-folder:before {
	content: "\ea83";
}
.codicon-logo-github:before {
	content: "\ea84";
}
.codicon-mark-github:before {
	content: "\ea84";
}
.codicon-github:before {
	content: "\ea84";
}
.codicon-terminal:before {
	content: "\ea85";
}
.codicon-console:before {
	content: "\ea85";
}
.codicon-repl:before {
	content: "\ea85";
}
.codicon-zap:before {
	content: "\ea86";
}
.codicon-symbol-event:before {
	content: "\ea86";
}
.codicon-error:before {
	content: "\ea87";
}
.codicon-stop:before {
	content: "\ea87";
}
.codicon-variable:before {
	content: "\ea88";
}
.codicon-symbol-variable:before {
	content: "\ea88";
}
.codicon-array:before {
	content: "\ea8a";
}
.codicon-symbol-array:before {
	content: "\ea8a";
}
.codicon-symbol-module:before {
	content: "\ea8b";
}
.codicon-symbol-package:before {
	content: "\ea8b";
}
.codicon-symbol-namespace:before {
	content: "\ea8b";
}
.codicon-symbol-object:before {
	content: "\ea8b";
}
.codicon-symbol-method:before {
	content: "\ea8c";
}
.codicon-symbol-function:before {
	content: "\ea8c";
}
.codicon-symbol-constructor:before {
	content: "\ea8c";
}
.codicon-symbol-boolean:before {
	content: "\ea8f";
}
.codicon-symbol-null:before {
	content: "\ea8f";
}
.codicon-symbol-numeric:before {
	content: "\ea90";
}
.codicon-symbol-number:before {
	content: "\ea90";
}
.codicon-symbol-structure:before {
	content: "\ea91";
}
.codicon-symbol-struct:before {
	content: "\ea91";
}
.codicon-symbol-parameter:before {
	content: "\ea92";
}
.codicon-symbol-type-parameter:before {
	content: "\ea92";
}
.codicon-symbol-key:before {
	content: "\ea93";
}
.codicon-symbol-text:before {
	content: "\ea93";
}
.codicon-symbol-reference:before {
	content: "\ea94";
}
.codicon-go-to-file:before {
	content: "\ea94";
}
.codicon-symbol-enum:before {
	content: "\ea95";
}
.codicon-symbol-value:before {
	content: "\ea95";
}
.codicon-symbol-ruler:before {
	content: "\ea96";
}
.codicon-symbol-unit:before {
	content: "\ea96";
}
.codicon-activate-breakpoints:before {
	content: "\ea97";
}
.codicon-archive:before {
	content: "\ea98";
}
.codicon-arrow-both:before {
	content: "\ea99";
}
.codicon-arrow-down:before {
	content: "\ea9a";
}
.codicon-arrow-left:before {
	content: "\ea9b";
}
.codicon-arrow-right:before {
	content: "\ea9c";
}
.codicon-arrow-small-down:before {
	content: "\ea9d";
}
.codicon-arrow-small-left:before {
	content: "\ea9e";
}
.codicon-arrow-small-right:before {
	content: "\ea9f";
}
.codicon-arrow-small-up:before {
	content: "\eaa0";
}
.codicon-arrow-up:before {
	content: "\eaa1";
}
.codicon-bell:before {
	content: "\eaa2";
}
.codicon-bold:before {
	content: "\eaa3";
}
.codicon-book:before {
	content: "\eaa4";
}
.codicon-bookmark:before {
	content: "\eaa5";
}
.codicon-debug-breakpoint-conditional-unverified:before {
	content: "\eaa6";
}
.codicon-debug-breakpoint-conditional:before {
	content: "\eaa7";
}
.codicon-debug-breakpoint-conditional-disabled:before {
	content: "\eaa7";
}
.codicon-debug-breakpoint-data-unverified:before {
	content: "\eaa8";
}
.codicon-debug-breakpoint-data:before {
	content: "\eaa9";
}
.codicon-debug-breakpoint-data-disabled:before {
	content: "\eaa9";
}
.codicon-debug-breakpoint-log-unverified:before {
	content: "\eaaa";
}
.codicon-debug-breakpoint-log:before {
	content: "\eaab";
}
.codicon-debug-breakpoint-log-disabled:before {
	content: "\eaab";
}
.codicon-briefcase:before {
	content: "\eaac";
}
.codicon-broadcast:before {
	content: "\eaad";
}
.codicon-browser:before {
	content: "\eaae";
}
.codicon-bug:before {
	content: "\eaaf";
}
.codicon-calendar:before {
	content: "\eab0";
}
.codicon-case-sensitive:before {
	content: "\eab1";
}
.codicon-check:before {
	content: "\eab2";
}
.codicon-checklist:before {
	content: "\eab3";
}
.codicon-chevron-down:before {
	content: "\eab4";
}
.codicon-chevron-left:before {
	content: "\eab5";
}
.codicon-chevron-right:before {
	content: "\eab6";
}
.codicon-chevron-up:before {
	content: "\eab7";
}
.codicon-chrome-close:before {
	content: "\eab8";
}
.codicon-chrome-maximize:before {
	content: "\eab9";
}
.codicon-chrome-minimize:before {
	content: "\eaba";
}
.codicon-chrome-restore:before {
	content: "\eabb";
}
.codicon-circle-outline:before {
	content: "\eabc";
}
.codicon-circle:before {
	content: "\eabc";
}
.codicon-debug-breakpoint-unverified:before {
	content: "\eabc";
}
.codicon-terminal-decoration-incomplete:before {
	content: "\eabc";
}
.codicon-circle-slash:before {
	content: "\eabd";
}
.codicon-circuit-board:before {
	content: "\eabe";
}
.codicon-clear-all:before {
	content: "\eabf";
}
.codicon-clippy:before {
	content: "\eac0";
}
.codicon-close-all:before {
	content: "\eac1";
}
.codicon-cloud-download:before {
	content: "\eac2";
}
.codicon-cloud-upload:before {
	content: "\eac3";
}
.codicon-code:before {
	content: "\eac4";
}
.codicon-collapse-all:before {
	content: "\eac5";
}
.codicon-color-mode:before {
	content: "\eac6";
}
.codicon-comment-discussion:before {
	content: "\eac7";
}
.codicon-credit-card:before {
	content: "\eac9";
}
.codicon-dash:before {
	content: "\eacc";
}
.codicon-dashboard:before {
	content: "\eacd";
}
.codicon-database:before {
	content: "\eace";
}
.codicon-debug-continue:before {
	content: "\eacf";
}
.codicon-debug-disconnect:before {
	content: "\ead0";
}
.codicon-debug-pause:before {
	content: "\ead1";
}
.codicon-debug-restart:before {
	content: "\ead2";
}
.codicon-debug-start:before {
	content: "\ead3";
}
.codicon-debug-step-into:before {
	content: "\ead4";
}
.codicon-debug-step-out:before {
	content: "\ead5";
}
.codicon-debug-step-over:before {
	content: "\ead6";
}
.codicon-debug-stop:before {
	content: "\ead7";
}
.codicon-debug:before {
	content: "\ead8";
}
.codicon-device-camera-video:before {
	content: "\ead9";
}
.codicon-device-camera:before {
	content: "\eada";
}
.codicon-device-mobile:before {
	content: "\eadb";
}
.codicon-diff-added:before {
	content: "\eadc";
}
.codicon-diff-ignored:before {
	content: "\eadd";
}
.codicon-diff-modified:before {
	content: "\eade";
}
.codicon-diff-removed:before {
	content: "\eadf";
}
.codicon-diff-renamed:before {
	content: "\eae0";
}
.codicon-diff:before {
	content: "\eae1";
}
.codicon-diff-sidebyside:before {
	content: "\eae1";
}
.codicon-discard:before {
	content: "\eae2";
}
.codicon-editor-layout:before {
	content: "\eae3";
}
.codicon-empty-window:before {
	content: "\eae4";
}
.codicon-exclude:before {
	content: "\eae5";
}
.codicon-extensions:before {
	content: "\eae6";
}
.codicon-eye-closed:before {
	content: "\eae7";
}
.codicon-file-binary:before {
	content: "\eae8";
}
.codicon-file-code:before {
	content: "\eae9";
}
.codicon-file-media:before {
	content: "\eaea";
}
.codicon-file-pdf:before {
	content: "\eaeb";
}
.codicon-file-submodule:before {
	content: "\eaec";
}
.codicon-file-symlink-directory:before {
	content: "\eaed";
}
.codicon-file-symlink-file:before {
	content: "\eaee";
}
.codicon-file-zip:before {
	content: "\eaef";
}
.codicon-files:before {
	content: "\eaf0";
}
.codicon-filter:before {
	content: "\eaf1";
}
.codicon-flame:before {
	content: "\eaf2";
}
.codicon-fold-down:before {
	content: "\eaf3";
}
.codicon-fold-up:before {
	content: "\eaf4";
}
.codicon-fold:before {
	content: "\eaf5";
}
.codicon-folder-active:before {
	content: "\eaf6";
}
.codicon-folder-opened:before {
	content: "\eaf7";
}
.codicon-gear:before {
	content: "\eaf8";
}
.codicon-gift:before {
	content: "\eaf9";
}
.codicon-gist-secret:before {
	content: "\eafa";
}
.codicon-gist:before {
	content: "\eafb";
}
.codicon-git-commit:before {
	content: "\eafc";
}
.codicon-git-compare:before {
	content: "\eafd";
}
.codicon-compare-changes:before {
	content: "\eafd";
}
.codicon-git-merge:before {
	content: "\eafe";
}
.codicon-github-action:before {
	content: "\eaff";
}
.codicon-github-alt:before {
	content: "\eb00";
}
.codicon-globe:before {
	content: "\eb01";
}
.codicon-grabber:before {
	content: "\eb02";
}
.codicon-graph:before {
	content: "\eb03";
}
.codicon-gripper:before {
	content: "\eb04";
}
.codicon-heart:before {
	content: "\eb05";
}
.codicon-home:before {
	content: "\eb06";
}
.codicon-horizontal-rule:before {
	content: "\eb07";
}
.codicon-hubot:before {
	content: "\eb08";
}
.codicon-inbox:before {
	content: "\eb09";
}
.codicon-issue-reopened:before {
	content: "\eb0b";
}
.codicon-issues:before {
	content: "\eb0c";
}
.codicon-italic:before {
	content: "\eb0d";
}
.codicon-jersey:before {
	content: "\eb0e";
}
.codicon-json:before {
	content: "\eb0f";
}
.codicon-kebab-vertical:before {
	content: "\eb10";
}
.codicon-key:before {
	content: "\eb11";
}
.codicon-law:before {
	content: "\eb12";
}
.codicon-lightbulb-autofix:before {
	content: "\eb13";
}
.codicon-link-external:before {
	content: "\eb14";
}
.codicon-link:before {
	content: "\eb15";
}
.codicon-list-ordered:before {
	content: "\eb16";
}
.codicon-list-unordered:before {
	content: "\eb17";
}
.codicon-live-share:before {
	content: "\eb18";
}
.codicon-loading:before {
	content: "\eb19";
}
.codicon-location:before {
	content: "\eb1a";
}
.codicon-mail-read:before {
	content: "\eb1b";
}
.codicon-mail:before {
	content: "\eb1c";
}
.codicon-markdown:before {
	content: "\eb1d";
}
.codicon-megaphone:before {
	content: "\eb1e";
}
.codicon-mention:before {
	content: "\eb1f";
}
.codicon-milestone:before {
	content: "\eb20";
}
.codicon-git-pull-request-milestone:before {
	content: "\eb20";
}
.codicon-mortar-board:before {
	content: "\eb21";
}
.codicon-move:before {
	content: "\eb22";
}
.codicon-multiple-windows:before {
	content: "\eb23";
}
.codicon-mute:before {
	content: "\eb24";
}
.codicon-no-newline:before {
	content: "\eb25";
}
.codicon-note:before {
	content: "\eb26";
}
.codicon-octoface:before {
	content: "\eb27";
}
.codicon-open-preview:before {
	content: "\eb28";
}
.codicon-package:before {
	content: "\eb29";
}
.codicon-paintcan:before {
	content: "\eb2a";
}
.codicon-pin:before {
	content: "\eb2b";
}
.codicon-play:before {
	content: "\eb2c";
}
.codicon-run:before {
	content: "\eb2c";
}
.codicon-plug:before {
	content: "\eb2d";
}
.codicon-preserve-case:before {
	content: "\eb2e";
}
.codicon-preview:before {
	content: "\eb2f";
}
.codicon-project:before {
	content: "\eb30";
}
.codicon-pulse:before {
	content: "\eb31";
}
.codicon-question:before {
	content: "\eb32";
}
.codicon-quote:before {
	content: "\eb33";
}
.codicon-radio-tower:before {
	content: "\eb34";
}
.codicon-reactions:before {
	content: "\eb35";
}
.codicon-references:before {
	content: "\eb36";
}
.codicon-refresh:before {
	content: "\eb37";
}
.codicon-regex:before {
	content: "\eb38";
}
.codicon-remote-explorer:before {
	content: "\eb39";
}
.codicon-remote:before {
	content: "\eb3a";
}
.codicon-remove:before {
	content: "\eb3b";
}
.codicon-replace-all:before {
	content: "\eb3c";
}
.codicon-replace:before {
	content: "\eb3d";
}
.codicon-repo-clone:before {
	content: "\eb3e";
}
.codicon-repo-force-push:before {
	content: "\eb3f";
}
.codicon-repo-pull:before {
	content: "\eb40";
}
.codicon-repo-push:before {
	content: "\eb41";
}
.codicon-report:before {
	content: "\eb42";
}
.codicon-request-changes:before {
	content: "\eb43";
}
.codicon-rocket:before {
	content: "\eb44";
}
.codicon-root-folder-opened:before {
	content: "\eb45";
}
.codicon-root-folder:before {
	content: "\eb46";
}
.codicon-rss:before {
	content: "\eb47";
}
.codicon-ruby:before {
	content: "\eb48";
}
.codicon-save-all:before {
	content: "\eb49";
}
.codicon-save-as:before {
	content: "\eb4a";
}
.codicon-save:before {
	content: "\eb4b";
}
.codicon-screen-full:before {
	content: "\eb4c";
}
.codicon-screen-normal:before {
	content: "\eb4d";
}
.codicon-search-stop:before {
	content: "\eb4e";
}
.codicon-server:before {
	content: "\eb50";
}
.codicon-settings-gear:before {
	content: "\eb51";
}
.codicon-settings:before {
	content: "\eb52";
}
.codicon-shield:before {
	content: "\eb53";
}
.codicon-smiley:before {
	content: "\eb54";
}
.codicon-sort-precedence:before {
	content: "\eb55";
}
.codicon-split-horizontal:before {
	content: "\eb56";
}
.codicon-split-vertical:before {
	content: "\eb57";
}
.codicon-squirrel:before {
	content: "\eb58";
}
.codicon-star-full:before {
	content: "\eb59";
}
.codicon-star-half:before {
	content: "\eb5a";
}
.codicon-symbol-class:before {
	content: "\eb5b";
}
.codicon-symbol-color:before {
	content: "\eb5c";
}
.codicon-symbol-constant:before {
	content: "\eb5d";
}
.codicon-symbol-enum-member:before {
	content: "\eb5e";
}
.codicon-symbol-field:before {
	content: "\eb5f";
}
.codicon-symbol-file:before {
	content: "\eb60";
}
.codicon-symbol-interface:before {
	content: "\eb61";
}
.codicon-symbol-keyword:before {
	content: "\eb62";
}
.codicon-symbol-misc:before {
	content: "\eb63";
}
.codicon-symbol-operator:before {
	content: "\eb64";
}
.codicon-symbol-property:before {
	content: "\eb65";
}
.codicon-wrench:before {
	content: "\eb65";
}
.codicon-wrench-subaction:before {
	content: "\eb65";
}
.codicon-symbol-snippet:before {
	content: "\eb66";
}
.codicon-tasklist:before {
	content: "\eb67";
}
.codicon-telescope:before {
	content: "\eb68";
}
.codicon-text-size:before {
	content: "\eb69";
}
.codicon-three-bars:before {
	content: "\eb6a";
}
.codicon-thumbsdown:before {
	content: "\eb6b";
}
.codicon-thumbsup:before {
	content: "\eb6c";
}
.codicon-tools:before {
	content: "\eb6d";
}
.codicon-triangle-down:before {
	content: "\eb6e";
}
.codicon-triangle-left:before {
	content: "\eb6f";
}
.codicon-triangle-right:before {
	content: "\eb70";
}
.codicon-triangle-up:before {
	content: "\eb71";
}
.codicon-twitter:before {
	content: "\eb72";
}
.codicon-unfold:before {
	content: "\eb73";
}
.codicon-unlock:before {
	content: "\eb74";
}
.codicon-unmute:before {
	content: "\eb75";
}
.codicon-unverified:before {
	content: "\eb76";
}
.codicon-verified:before {
	content: "\eb77";
}
.codicon-versions:before {
	content: "\eb78";
}
.codicon-vm-active:before {
	content: "\eb79";
}
.codicon-vm-outline:before {
	content: "\eb7a";
}
.codicon-vm-running:before {
	content: "\eb7b";
}
.codicon-watch:before {
	content: "\eb7c";
}
.codicon-whitespace:before {
	content: "\eb7d";
}
.codicon-whole-word:before {
	content: "\eb7e";
}
.codicon-window:before {
	content: "\eb7f";
}
.codicon-word-wrap:before {
	content: "\eb80";
}
.codicon-zoom-in:before {
	content: "\eb81";
}
.codicon-zoom-out:before {
	content: "\eb82";
}
.codicon-list-filter:before {
	content: "\eb83";
}
.codicon-list-flat:before {
	content: "\eb84";
}
.codicon-list-selection:before {
	content: "\eb85";
}
.codicon-selection:before {
	content: "\eb85";
}
.codicon-list-tree:before {
	content: "\eb86";
}
.codicon-debug-breakpoint-function-unverified:before {
	content: "\eb87";
}
.codicon-debug-breakpoint-function:before {
	content: "\eb88";
}
.codicon-debug-breakpoint-function-disabled:before {
	content: "\eb88";
}
.codicon-debug-stackframe-active:before {
	content: "\eb89";
}
.codicon-circle-small-filled:before {
	content: "\eb8a";
}
.codicon-debug-stackframe-dot:before {
	content: "\eb8a";
}
.codicon-terminal-decoration-mark:before {
	content: "\eb8a";
}
.codicon-debug-stackframe:before {
	content: "\eb8b";
}
.codicon-debug-stackframe-focused:before {
	content: "\eb8b";
}
.codicon-debug-breakpoint-unsupported:before {
	content: "\eb8c";
}
.codicon-symbol-string:before {
	content: "\eb8d";
}
.codicon-debug-reverse-continue:before {
	content: "\eb8e";
}
.codicon-debug-step-back:before {
	content: "\eb8f";
}
.codicon-debug-restart-frame:before {
	content: "\eb90";
}
.codicon-debug-alt:before {
	content: "\eb91";
}
.codicon-call-incoming:before {
	content: "\eb92";
}
.codicon-call-outgoing:before {
	content: "\eb93";
}
.codicon-menu:before {
	content: "\eb94";
}
.codicon-expand-all:before {
	content: "\eb95";
}
.codicon-feedback:before {
	content: "\eb96";
}
.codicon-git-pull-request-reviewer:before {
	content: "\eb96";
}
.codicon-group-by-ref-type:before {
	content: "\eb97";
}
.codicon-ungroup-by-ref-type:before {
	content: "\eb98";
}
.codicon-account:before {
	content: "\eb99";
}
.codicon-git-pull-request-assignee:before {
	content: "\eb99";
}
.codicon-bell-dot:before {
	content: "\eb9a";
}
.codicon-debug-console:before {
	content: "\eb9b";
}
.codicon-library:before {
	content: "\eb9c";
}
.codicon-output:before {
	content: "\eb9d";
}
.codicon-run-all:before {
	content: "\eb9e";
}
.codicon-sync-ignored:before {
	content: "\eb9f";
}
.codicon-pinned:before {
	content: "\eba0";
}
.codicon-github-inverted:before {
	content: "\eba1";
}
.codicon-server-process:before {
	content: "\eba2";
}
.codicon-server-environment:before {
	content: "\eba3";
}
.codicon-pass:before {
	content: "\eba4";
}
.codicon-issue-closed:before {
	content: "\eba4";
}
.codicon-stop-circle:before {
	content: "\eba5";
}
.codicon-play-circle:before {
	content: "\eba6";
}
.codicon-record:before {
	content: "\eba7";
}
.codicon-debug-alt-small:before {
	content: "\eba8";
}
.codicon-vm-connect:before {
	content: "\eba9";
}
.codicon-cloud:before {
	content: "\ebaa";
}
.codicon-merge:before {
	content: "\ebab";
}
.codicon-export:before {
	content: "\ebac";
}
.codicon-graph-left:before {
	content: "\ebad";
}
.codicon-magnet:before {
	content: "\ebae";
}
.codicon-notebook:before {
	content: "\ebaf";
}
.codicon-redo:before {
	content: "\ebb0";
}
.codicon-check-all:before {
	content: "\ebb1";
}
.codicon-pinned-dirty:before {
	content: "\ebb2";
}
.codicon-pass-filled:before {
	content: "\ebb3";
}
.codicon-circle-large-filled:before {
	content: "\ebb4";
}
.codicon-circle-large:before {
	content: "\ebb5";
}
.codicon-circle-large-outline:before {
	content: "\ebb5";
}
.codicon-combine:before {
	content: "\ebb6";
}
.codicon-gather:before {
	content: "\ebb6";
}
.codicon-table:before {
	content: "\ebb7";
}
.codicon-variable-group:before {
	content: "\ebb8";
}
.codicon-type-hierarchy:before {
	content: "\ebb9";
}
.codicon-type-hierarchy-sub:before {
	content: "\ebba";
}
.codicon-type-hierarchy-super:before {
	content: "\ebbb";
}
.codicon-git-pull-request-create:before {
	content: "\ebbc";
}
.codicon-run-above:before {
	content: "\ebbd";
}
.codicon-run-below:before {
	content: "\ebbe";
}
.codicon-notebook-template:before {
	content: "\ebbf";
}
.codicon-debug-rerun:before {
	content: "\ebc0";
}
.codicon-workspace-trusted:before {
	content: "\ebc1";
}
.codicon-workspace-untrusted:before {
	content: "\ebc2";
}
.codicon-workspace-unknown:before {
	content: "\ebc3";
}
.codicon-terminal-cmd:before {
	content: "\ebc4";
}
.codicon-terminal-debian:before {
	content: "\ebc5";
}
.codicon-terminal-linux:before {
	content: "\ebc6";
}
.codicon-terminal-powershell:before {
	content: "\ebc7";
}
.codicon-terminal-tmux:before {
	content: "\ebc8";
}
.codicon-terminal-ubuntu:before {
	content: "\ebc9";
}
.codicon-terminal-bash:before {
	content: "\ebca";
}
.codicon-arrow-swap:before {
	content: "\ebcb";
}
.codicon-copy:before {
	content: "\ebcc";
}
.codicon-person-add:before {
	content: "\ebcd";
}
.codicon-filter-filled:before {
	content: "\ebce";
}
.codicon-wand:before {
	content: "\ebcf";
}
.codicon-debug-line-by-line:before {
	content: "\ebd0";
}
.codicon-inspect:before {
	content: "\ebd1";
}
.codicon-layers:before {
	content: "\ebd2";
}
.codicon-layers-dot:before {
	content: "\ebd3";
}
.codicon-layers-active:before {
	content: "\ebd4";
}
.codicon-compass:before {
	content: "\ebd5";
}
.codicon-compass-dot:before {
	content: "\ebd6";
}
.codicon-compass-active:before {
	content: "\ebd7";
}
.codicon-azure:before {
	content: "\ebd8";
}
.codicon-issue-draft:before {
	content: "\ebd9";
}
.codicon-git-pull-request-closed:before {
	content: "\ebda";
}
.codicon-git-pull-request-draft:before {
	content: "\ebdb";
}
.codicon-debug-all:before {
	content: "\ebdc";
}
.codicon-debug-coverage:before {
	content: "\ebdd";
}
.codicon-run-errors:before {
	content: "\ebde";
}
.codicon-folder-library:before {
	content: "\ebdf";
}
.codicon-debug-continue-small:before {
	content: "\ebe0";
}
.codicon-beaker-stop:before {
	content: "\ebe1";
}
.codicon-graph-line:before {
	content: "\ebe2";
}
.codicon-graph-scatter:before {
	content: "\ebe3";
}
.codicon-pie-chart:before {
	content: "\ebe4";
}
.codicon-bracket:before {
	content: "\eb0f";
}
.codicon-bracket-dot:before {
	content: "\ebe5";
}
.codicon-bracket-error:before {
	content: "\ebe6";
}
.codicon-lock-small:before {
	content: "\ebe7";
}
.codicon-azure-devops:before {
	content: "\ebe8";
}
.codicon-verified-filled:before {
	content: "\ebe9";
}
.codicon-newline:before {
	content: "\ebea";
}
.codicon-layout:before {
	content: "\ebeb";
}
.codicon-layout-activitybar-left:before {
	content: "\ebec";
}
.codicon-layout-activitybar-right:before {
	content: "\ebed";
}
.codicon-layout-panel-left:before {
	content: "\ebee";
}
.codicon-layout-panel-center:before {
	content: "\ebef";
}
.codicon-layout-panel-justify:before {
	content: "\ebf0";
}
.codicon-layout-panel-right:before {
	content: "\ebf1";
}
.codicon-layout-panel:before {
	content: "\ebf2";
}
.codicon-layout-sidebar-left:before {
	content: "\ebf3";
}
.codicon-layout-sidebar-right:before {
	content: "\ebf4";
}
.codicon-layout-statusbar:before {
	content: "\ebf5";
}
.codicon-layout-menubar:before {
	content: "\ebf6";
}
.codicon-layout-centered:before {
	content: "\ebf7";
}
.codicon-target:before {
	content: "\ebf8";
}
.codicon-indent:before {
	content: "\ebf9";
}
.codicon-record-small:before {
	content: "\ebfa";
}
.codicon-error-small:before {
	content: "\ebfb";
}
.codicon-terminal-decoration-error:before {
	content: "\ebfb";
}
.codicon-arrow-circle-down:before {
	content: "\ebfc";
}
.codicon-arrow-circle-left:before {
	content: "\ebfd";
}
.codicon-arrow-circle-right:before {
	content: "\ebfe";
}
.codicon-arrow-circle-up:before {
	content: "\ebff";
}
.codicon-layout-sidebar-right-off:before {
	content: "\ec00";
}
.codicon-layout-panel-off:before {
	content: "\ec01";
}
.codicon-layout-sidebar-left-off:before {
	content: "\ec02";
}
.codicon-blank:before {
	content: "\ec03";
}
.codicon-heart-filled:before {
	content: "\ec04";
}
.codicon-map:before {
	content: "\ec05";
}
.codicon-map-horizontal:before {
	content: "\ec05";
}
.codicon-fold-horizontal:before {
	content: "\ec05";
}
.codicon-map-filled:before {
	content: "\ec06";
}
.codicon-map-horizontal-filled:before {
	content: "\ec06";
}
.codicon-fold-horizontal-filled:before {
	content: "\ec06";
}
.codicon-circle-small:before {
	content: "\ec07";
}
.codicon-bell-slash:before {
	content: "\ec08";
}
.codicon-bell-slash-dot:before {
	content: "\ec09";
}
.codicon-comment-unresolved:before {
	content: "\ec0a";
}
.codicon-git-pull-request-go-to-changes:before {
	content: "\ec0b";
}
.codicon-git-pull-request-new-changes:before {
	content: "\ec0c";
}
.codicon-search-fuzzy:before {
	content: "\ec0d";
}
.codicon-comment-draft:before {
	content: "\ec0e";
}
.codicon-send:before {
	content: "\ec0f";
}
.codicon-sparkle:before {
	content: "\ec10";
}
.codicon-insert:before {
	content: "\ec11";
}
.codicon-mic:before {
	content: "\ec12";
}
.codicon-thumbsdown-filled:before {
	content: "\ec13";
}
.codicon-thumbsup-filled:before {
	content: "\ec14";
}
.codicon-coffee:before {
	content: "\ec15";
}
.codicon-snake:before {
	content: "\ec16";
}
.codicon-game:before {
	content: "\ec17";
}
.codicon-vr:before {
	content: "\ec18";
}
.codicon-chip:before {
	content: "\ec19";
}
.codicon-piano:before {
	content: "\ec1a";
}
.codicon-music:before {
	content: "\ec1b";
}
.codicon-mic-filled:before {
	content: "\ec1c";
}
.codicon-repo-fetch:before {
	content: "\ec1d";
}
.codicon-copilot:before {
	content: "\ec1e";
}
.codicon-lightbulb-sparkle:before {
	content: "\ec1f";
}
.codicon-robot:before {
	content: "\ec20";
}
.codicon-sparkle-filled:before {
	content: "\ec21";
}
.codicon-diff-single:before {
	content: "\ec22";
}
.codicon-diff-multiple:before {
	content: "\ec23";
}
.codicon-surround-with:before {
	content: "\ec24";
}
.codicon-share:before {
	content: "\ec25";
}
.codicon-git-stash:before {
	content: "\ec26";
}
.codicon-git-stash-apply:before {
	content: "\ec27";
}
.codicon-git-stash-pop:before {
	content: "\ec28";
}
.codicon-vscode:before {
	content: "\ec29";
}
.codicon-vscode-insiders:before {
	content: "\ec2a";
}
.codicon-code-oss:before {
	content: "\ec2b";
}
.codicon-run-coverage:before {
	content: "\ec2c";
}
.codicon-run-all-coverage:before {
	content: "\ec2d";
}
.codicon-coverage:before {
	content: "\ec2e";
}
.codicon-github-project:before {
	content: "\ec2f";
}
.codicon-map-vertical:before {
	content: "\ec30";
}
.codicon-fold-vertical:before {
	content: "\ec30";
}
.codicon-map-vertical-filled:before {
	content: "\ec31";
}
.codicon-fold-vertical-filled:before {
	content: "\ec31";
}
.codicon-go-to-search:before {
	content: "\ec32";
}
.codicon-percentage:before {
	content: "\ec33";
}
.codicon-sort-percentage:before {
	content: "\ec33";
}
.codicon-git-fetch:before {
	content: "\f101";
}
