import React from "react"
import { BookO<PERSON> } from "lucide-react"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { vscode } from "@src/utils/vscode"
import { useAuth } from "@src/context/AuthContext"
import { But<PERSON> } from "@src/components/ui/button"

export const AuthenticationRequired = () => {
	const { t } = useAppTranslation()
	const { isAuthenticating, authError, clearError, signIn } = useAuth()

	const handleSignIn = () => {
		clearError() // Clear any previous errors
		signIn() // Update local state
		vscode.postMessage({ type: "deviceOAuthSignIn" })
	}

	const handleHelpClick = () => {
		vscode.postMessage({
			type: "openExternal",
			url: "https://docs.cubent.com",
		})
	}

	return (
		<div className="flex-1 flex items-center justify-center bg-vscode-editor-background text-vscode-foreground p-4">
			<div className="w-full px-4 sm:px-0 sm:max-w-sm animate-in fade-in-0 zoom-in-95 duration-400">
				{/* Card Container */}
				<div className="bg-vscode-sideBar-background border border-vscode-panel-border shadow-lg rounded-2xl overflow-hidden">
					{/* Card Header */}
					<div className="space-y-3 p-6 pb-4">
						{/* Heading */}
						<h1 className="text-center text-lg font-bold text-vscode-foreground">
							Authentication Required
						</h1>

						{/* Description */}
						<p className="text-center text-vscode-descriptionForeground leading-relaxed text-sm">
							Sign in to <span className="font-semibold text-vscode-foreground">Cubent&nbsp;Coder</span> to securely access AI models and your personalized settings.
						</p>
					</div>

					{/* Card Content */}
					<div className="space-y-6 p-6 pt-2">
						<p className="text-center text-xs text-vscode-descriptionForeground">
							Your session will stay signed&nbsp;in across VS&nbsp;Code launches until you manually sign&nbsp;out.
						</p>

						{/* Error Message */}
						{authError && (
							<div className="p-3 bg-vscode-inputValidation-errorBackground border border-vscode-inputValidation-errorBorder rounded-lg">
								<p className="text-vscode-inputValidation-errorForeground text-sm text-center">{authError}</p>
							</div>
						)}

						{/* Sign In Button */}
						<Button
							onClick={handleSignIn}
							disabled={isAuthenticating}
							className="w-full py-3 text-base font-semibold"
							variant="default"
						>
							{isAuthenticating ? "Signing in..." : "Sign in"}
						</Button>

						{/* Help Link */}
						<button
							onClick={handleHelpClick}
							className="flex items-center justify-center gap-1 text-xs text-vscode-textLink-foreground hover:text-vscode-textLink-activeForeground transition-colors w-full bg-transparent border-none cursor-pointer"
						>
							<BookOpen size={14} aria-hidden="true" />
							Need&nbsp;help?&nbsp;Read&nbsp;docs
						</button>
					</div>
				</div>
			</div>
		</div>
	)
}
