import React from "react"
import { BookOpen } from "lucide-react"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { vscode } from "@src/utils/vscode"
import { useAuth } from "@src/context/AuthContext"
import { But<PERSON> } from "@src/components/ui/button"

export const AuthenticationRequired = () => {
	const { t } = useAppTranslation()
	const { isAuthenticating, authError, clearError, signIn } = useAuth()

	const handleSignIn = () => {
		clearError() // Clear any previous errors
		signIn() // Update local state
		vscode.postMessage({ type: "deviceOAuthSignIn" })
	}

	const handleHelpClick = () => {
		vscode.postMessage({
			type: "openExternal",
			url: "https://docs.cubent.com",
		})
	}

	return (
		<div className="min-h-screen flex items-center justify-center bg-zinc-950 text-zinc-100 p-4">
			<div className="w-full px-4 sm:px-0 sm:max-w-sm animate-in fade-in-0 zoom-in-95 duration-400">
				{/* Card Container */}
				<div className="bg-zinc-900/90 border border-zinc-800 shadow-xl rounded-2xl overflow-hidden">
					{/* Card Header */}
					<div className="space-y-3 p-6 pb-4">
						{/* Heading */}
						<h1 className="text-center text-2xl font-bold text-white">
							Authentication Required
						</h1>

						{/* Description */}
						<p className="text-center text-zinc-400 leading-relaxed">
							Sign in to <span className="font-semibold text-white">Cubent&nbsp;Coder</span> to securely access AI models and your personalized settings.
						</p>
					</div>

					{/* Card Content */}
					<div className="space-y-6 p-6 pt-2">
						<p className="text-center text-sm text-zinc-400">
							Your session will stay signed&nbsp;in across VS&nbsp;Code launches until you manually sign&nbsp;out.
						</p>

						{/* Error Message */}
						{authError && (
							<div className="p-3 bg-red-900/20 border border-red-800 rounded-lg">
								<p className="text-red-400 text-sm text-center">{authError}</p>
							</div>
						)}

						{/* Sign In Button */}
						<Button
							onClick={handleSignIn}
							disabled={isAuthenticating}
							className="w-full py-3 text-base font-semibold bg-zinc-800 hover:bg-zinc-700 text-white ring-offset-zinc-800 focus:ring-2 focus:ring-zinc-600 border-0"
						>
							{isAuthenticating ? "Signing in..." : "Sign in"}
						</Button>

						{/* Help Link */}
						<button
							onClick={handleHelpClick}
							className="flex items-center justify-center gap-1 text-xs text-zinc-400 hover:text-zinc-200 transition-colors w-full bg-transparent border-none cursor-pointer"
						>
							<BookOpen size={14} aria-hidden="true" />
							Need&nbsp;help?&nbsp;Read&nbsp;docs
						</button>
					</div>
				</div>
			</div>
		</div>
	)
}
